-- Consolidated Formatter Configuration
-- Modern formatters only: <PERSON><PERSON> (Python), <PERSON><PERSON><PERSON> (JS/TS/JSON), <PERSON><PERSON><PERSON> (Lua)

local core = require("core.config")

return {
  -- ============================================================================
  -- CONFORM.NVIM - MAIN FORMATTER PLUGIN
  -- ============================================================================
  {
    "stevearc/conform.nvim",
    event = { "BufWritePre" },
    cmd = { "ConformInfo" },
    opts = {
      -- File type to formatter mapping (modern tools only)
      formatters_by_ft = {
        -- Python: Ruff only (replaces black, isort, flake8)
        python = { "ruff_format", "ruff_organize_imports" },

        -- Frontend: Biome only (replaces ESLint + Prettier)
        javascript = { "biome" },
        typescript = { "biome" },
        javascriptreact = { "biome" },
        typescriptreact = { "biome" },
        json = { "biome" },

        -- Lua: Stylua
        lua = { "stylua" },

        -- Config files
        toml = { "taplo" },

        -- Other files use LSP formatting
      },

      -- Formatter definitions
      formatters = {
        -- Ruff formatter (Python)
        ruff_format = {
          command = "ruff",
          args = { "format", "-" },
          stdin = true,
          exit_codes = { 0, 1 },
          condition = function()
            return core.is_available("ruff")
          end,
        },

        -- Ruff import organizer (Python)
        ruff_organize_imports = {
          command = "ruff",
          args = { "check", "--fix", "--select=I", "-" },
          stdin = true,
          exit_codes = { 0, 1 },
          condition = function()
            return core.is_available("ruff")
          end,
        },

        -- Biome formatter (JS/TS/JSON)
        biome = {
          command = "biome",
          args = { "format", "--stdin-file-path", "$FILENAME" },
          stdin = true,
          exit_codes = { 0, 1 },
          condition = function()
            return core.is_available("biome")
          end,
        },

        -- Stylua formatter (Lua)
        stylua = {
          command = "stylua",
          args = {
            "--indent-type=Spaces",
            "--indent-width=2",
            "--column-width=100",
            "--stdin-filepath", "$FILENAME",
            "-"
          },
          stdin = true,
          condition = function()
            return core.is_available("stylua")
          end,
        },

        -- Taplo formatter (TOML)
        taplo = {
          command = "taplo",
          args = { "format", "-" },
          stdin = true,
          condition = function()
            return core.is_available("taplo")
          end,
        },
      },

      -- Default format options
      default_format_opts = {
        timeout_ms = core.constants.performance.format_timeout,
        async = true,
        quiet = false,
        lsp_format = "fallback",
      },

      -- Note: format_on_save is handled automatically by LazyVim
      -- No need to configure it here as LazyVim will use conform formatter automatically
    },
  },



  -- ============================================================================
  -- LINTING (RUFF ONLY)
  -- ============================================================================
  {
    "mfussenegger/nvim-lint",
    event = { "BufReadPre", "BufNewFile" },
    opts = {
      linters_by_ft = {
        python = { "ruff" },
      },
      linters = {
        ruff = {
          cmd = "ruff",
          args = {
            "check",
            "--output-format", "text",
            "--stdin-filename", function() return vim.api.nvim_buf_get_name(0) end,
            "-",
          },
          stdin = true,
          stream = "stdout",
          ignore_exitcode = true,
          condition = function()
            return core.is_available("ruff")
          end,
        },
      },
    },
    config = function(_, opts)
      local lint = require("lint")
      lint.linters_by_ft = opts.linters_by_ft

      for name, config in pairs(opts.linters) do
        lint.linters[name] = config
      end

      -- Auto-lint on specific events
      vim.api.nvim_create_autocmd({ "BufEnter", "BufWritePost", "InsertLeave" }, {
        group = vim.api.nvim_create_augroup("nvim-lint", { clear = true }),
        callback = function()
          -- Only lint Python files
          if vim.bo.filetype == "python" then
            lint.try_lint()
          end
        end,
      })
    end,
  },


}
