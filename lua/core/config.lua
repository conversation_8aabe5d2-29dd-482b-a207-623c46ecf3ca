-- Core Configuration Module
-- Centralized constants, utilities, and shared functions for Python fullstack development

local M = {}

-- ============================================================================
-- CORE CONSTANTS
-- ============================================================================

M.constants = {
  -- Development tools (only modern, non-deprecated)
  tools = {
    python = {
      formatter = "ruff",
      linter = "ruff", 
      lsp = "pyright",
      debugger = "debugpy",
    },
    frontend = {
      formatter = "biome",
      linter = "biome",
      lsp = "typescript-language-server",
    },
    lua = {
      formatter = "stylua",
      lsp = "lua-language-server",
    },
  },

  -- File type mappings
  filetypes = {
    python = { "python" },
    frontend = { "javascript", "typescript", "javascriptreact", "typescriptreact", "json" },
    lua = { "lua" },
    config = { "yaml", "toml", "json" },
    markup = { "html", "css", "scss", "markdown" },
  },

  -- Project detection patterns
  project_patterns = {
    django = { "manage.py", "settings.py", "*/settings.py" },
    fastapi = { "main.py" }, -- Will check content for FastAPI imports
    python_general = { "pyproject.toml", "requirements.txt", "setup.py", "Pipfile" },
    frontend = { "package.json", "vite.config.js", "webpack.config.js", "next.config.js" },
  },

  -- Essential Mason packages (no deprecated tools)
  mason_packages = {
    -- Python ecosystem
    "ruff",                    -- Python formatter/linter (replaces black, isort, flake8)
    "pyright",                 -- Python LSP
    "debugpy",                 -- Python debugger
    
    -- Frontend ecosystem  
    "typescript-language-server", -- TypeScript LSP
    "biome",                   -- JS/TS formatter/linter (replaces ESLint + Prettier)
    
    -- Lua ecosystem
    "lua-language-server",     -- Lua LSP
    "stylua",                  -- Lua formatter
    
    -- Configuration
    "taplo",                   -- TOML formatter
  },

  -- Performance settings
  performance = {
    max_filesize = 100 * 1024, -- 100KB
    format_timeout = 3000,      -- 3 seconds
    lsp_timeout = 5000,         -- 5 seconds
  },
}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Check if a tool is available
function M.is_available(tool)
  return vim.fn.executable(tool) == 1
end

-- Get current file type
function M.get_filetype()
  return vim.bo.filetype
end

-- Check if current file is too large for formatting
function M.is_file_too_large(bufnr)
  bufnr = bufnr or 0
  local max_size = M.constants.performance.max_filesize
  local ok, stats = pcall(vim.loop.fs_stat, vim.api.nvim_buf_get_name(bufnr))
  return ok and stats and stats.size > max_size
end

-- Detect project type based on file patterns
function M.detect_project_type()
  local cwd = vim.fn.getcwd()
  local patterns = M.constants.project_patterns
  
  -- Check Django
  for _, pattern in ipairs(patterns.django) do
    if vim.fn.glob(cwd .. "/" .. pattern) ~= "" then
      return "django"
    end
  end
  
  -- Check FastAPI (requires content inspection)
  if vim.fn.filereadable(cwd .. "/main.py") == 1 then
    local content = vim.fn.readfile(cwd .. "/main.py", "", 10) -- Read first 10 lines
    for _, line in ipairs(content) do
      if line:match("fastapi") or line:match("FastAPI") then
        return "fastapi"
      end
    end
  end
  
  -- Check general Python project
  for _, pattern in ipairs(patterns.python_general) do
    if vim.fn.filereadable(cwd .. "/" .. pattern) == 1 then
      return "python"
    end
  end
  
  -- Check frontend project
  for _, pattern in ipairs(patterns.frontend) do
    if vim.fn.filereadable(cwd .. "/" .. pattern) == 1 then
      return "frontend"
    end
  end
  
  return "general"
end

-- Get appropriate formatter for file type
function M.get_formatter(filetype)
  filetype = filetype or M.get_filetype()
  
  if vim.tbl_contains(M.constants.filetypes.python, filetype) then
    return M.constants.tools.python.formatter
  elseif vim.tbl_contains(M.constants.filetypes.frontend, filetype) then
    return M.constants.tools.frontend.formatter
  elseif vim.tbl_contains(M.constants.filetypes.lua, filetype) then
    return M.constants.tools.lua.formatter
  else
    return "lsp" -- Fallback to LSP formatting
  end
end

-- Safe notification with fallback
function M.notify(message, level, opts)
  level = level or vim.log.levels.INFO
  opts = opts or {}
  
  local ok, _ = pcall(vim.notify, message, level, opts)
  if not ok then
    -- Fallback to print if vim.notify fails
    print(message)
  end
end

-- Safe require with error handling
function M.safe_require(module)
  local ok, result = pcall(require, module)
  if ok then
    return result
  else
    M.notify("Failed to load module: " .. module, vim.log.levels.WARN)
    return nil
  end
end

-- Check if plugin is loaded
function M.is_plugin_loaded(plugin_name)
  return M.safe_require(plugin_name) ~= nil
end

-- Create user command with error handling
function M.create_command(name, callback, opts)
  opts = opts or {}
  
  local safe_callback = function(...)
    local ok, err = pcall(callback, ...)
    if not ok then
      M.notify("Command '" .. name .. "' failed: " .. tostring(err), vim.log.levels.ERROR)
    end
  end
  
  vim.api.nvim_create_user_command(name, safe_callback, opts)
end

-- Create keymap with consistent options
function M.create_keymap(mode, lhs, rhs, opts)
  opts = vim.tbl_extend("force", {
    noremap = true,
    silent = true,
  }, opts or {})
  
  vim.keymap.set(mode, lhs, rhs, opts)
end

-- ============================================================================
-- VALIDATION FUNCTIONS
-- ============================================================================

-- Validate that essential tools are available
function M.validate_tools()
  local missing_tools = {}
  
  for category, tools in pairs(M.constants.tools) do
    for tool_type, tool_name in pairs(tools) do
      if not M.is_available(tool_name) then
        table.insert(missing_tools, string.format("%s (%s %s)", tool_name, category, tool_type))
      end
    end
  end
  
  return missing_tools
end

-- Get configuration status for health checks
function M.get_status()
  return {
    project_type = M.detect_project_type(),
    available_tools = {
      ruff = M.is_available("ruff"),
      biome = M.is_available("biome"),
      stylua = M.is_available("stylua"),
    },
    deprecated_tools = {
      prettier = M.is_available("prettier"),
      eslint = M.is_available("eslint"),
      black = M.is_available("black"),
      isort = M.is_available("isort"),
    },
  }
end

return M
