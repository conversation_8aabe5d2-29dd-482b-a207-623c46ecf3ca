-- Consolidated Commands and Keymaps
-- Essential commands for Python fullstack development

local core = require("core.config")

local M = {}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Execute command in terminal
local function exec_cmd(cmd, direction)
  direction = direction or "horizontal"
  local term_cmd = string.format("TermExec cmd='%s' direction=%s", cmd, direction)
  vim.cmd(term_cmd)
end

-- Check if we're in a Python project
local function is_python_project()
  local project_type = core.detect_project_type()
  return vim.tbl_contains({"django", "fastapi", "python"}, project_type)
end

-- Check if we're in a frontend project
local function is_frontend_project()
  local project_type = core.detect_project_type()
  return project_type == "frontend"
end

-- ============================================================================
-- DEVELOPMENT COMMANDS
-- ============================================================================

-- Python development commands
function M.python_commands()
  -- Django commands
  core.create_command("DjangoRunserver", function(opts)
    local port = opts.args ~= "" and opts.args or "8000"
    local cmd = string.format("python manage.py runserver 0.0.0.0:%s", port)
    exec_cmd(cmd, "horizontal")
  end, {
    desc = "Django runserver",
    nargs = "?",
  })

  core.create_command("DjangoMigrate", function()
    exec_cmd("python manage.py migrate", "horizontal")
  end, { desc = "Django migrate" })

  core.create_command("DjangoMakemigrations", function()
    exec_cmd("python manage.py makemigrations", "horizontal")
  end, { desc = "Django makemigrations" })

  core.create_command("DjangoShell", function()
    exec_cmd("python manage.py shell", "horizontal")
  end, { desc = "Django shell" })

  -- FastAPI commands
  core.create_command("FastAPIRun", function(opts)
    local port = opts.args ~= "" and opts.args or "8000"
    local cmd = string.format("uvicorn main:app --reload --host 0.0.0.0 --port %s", port)
    exec_cmd(cmd, "horizontal")
  end, {
    desc = "FastAPI runserver",
    nargs = "?",
  })

  -- General Python commands
  core.create_command("PythonREPL", function()
    exec_cmd("python", "horizontal")
  end, { desc = "Python REPL" })

  core.create_command("PipInstall", function(opts)
    if opts.args == "" then
      core.notify("Please specify package name", vim.log.levels.WARN)
      return
    end
    exec_cmd("pip install " .. opts.args, "horizontal")
  end, {
    desc = "Install Python package",
    nargs = 1,
  })
end

-- Frontend development commands
function M.frontend_commands()
  core.create_command("NpmInstall", function()
    exec_cmd("npm install", "horizontal")
  end, { desc = "npm install" })

  core.create_command("NpmDev", function()
    exec_cmd("npm run dev", "horizontal")
  end, { desc = "npm run dev" })

  core.create_command("NpmBuild", function()
    exec_cmd("npm run build", "horizontal")
  end, { desc = "npm run build" })

  core.create_command("NpmTest", function()
    exec_cmd("npm test", "horizontal")
  end, { desc = "npm test" })
end

-- Formatter commands
function M.formatter_commands()
  core.create_command("FormatBuffer", function()
    require("conform").format({ async = true, lsp_fallback = true })
  end, { desc = "Format current buffer" })

  core.create_command("FormatDiagnose", function()
    local conform = require("conform")
    local filetype = vim.bo.filetype
    local formatters = conform.list_formatters_for_buffer()
    
    print("=== Format Diagnostic ===")
    print("File type: " .. filetype)
    print("Available formatters:")
    
    if #formatters == 0 then
      print("  No formatters configured for this file type")
    else
      for _, formatter in ipairs(formatters) do
        local status = formatter.available and "✅ Available" or "❌ Not available"
        print(string.format("  %s: %s", formatter.name, status))
      end
    end
    
    print("\nFormatter status:")
    local tools = core.constants.tools
    for category, category_tools in pairs(tools) do
      if category_tools.formatter then
        local available = core.is_available(category_tools.formatter)
        local status = available and "✅" or "❌"
        print(string.format("  %s %s (%s)", status, category_tools.formatter, category))
      end
    end
  end, { desc = "Diagnose formatter configuration" })

  core.create_command("RuffCheck", function()
    if not core.is_available("ruff") then
      core.notify("Ruff not available", vim.log.levels.ERROR)
      return
    end
    exec_cmd("ruff check .", "horizontal")
  end, { desc = "Run Ruff linter" })

  core.create_command("RuffFormat", function()
    if not core.is_available("ruff") then
      core.notify("Ruff not available", vim.log.levels.ERROR)
      return
    end
    exec_cmd("ruff format .", "horizontal")
  end, { desc = "Run Ruff formatter" })
end

-- Mason commands
function M.mason_commands()
  core.create_command("MasonStatus", function()
    local mason_registry = require("mason-registry")
    local installed_packages = mason_registry.get_installed_packages()
    
    print("=== Mason Status ===")
    print("Installed packages:")
    
    if #installed_packages == 0 then
      print("  No packages installed")
    else
      for _, pkg in ipairs(installed_packages) do
        print("  ✅ " .. pkg.name)
      end
    end
    
    print("\nEssential packages status:")
    for _, tool in ipairs(core.constants.mason_packages) do
      local pkg = mason_registry.get_package(tool)
      local status = pkg:is_installed() and "✅ Installed" or "❌ Not installed"
      print(string.format("  %s: %s", tool, status))
    end
  end, { desc = "Show Mason package status" })

  core.create_command("MasonInstallEssentials", function()
    local mason_registry = require("mason-registry")
    
    for _, tool in ipairs(core.constants.mason_packages) do
      local pkg = mason_registry.get_package(tool)
      if not pkg:is_installed() then
        core.notify("Installing " .. tool, vim.log.levels.INFO)
        pkg:install()
      end
    end
  end, { desc = "Install essential Mason packages" })
end

-- Health check commands
function M.health_commands()
  core.create_command("HealthCheck", function()
    vim.cmd("checkhealth config")
  end, { desc = "Run configuration health check" })

  core.create_command("HealthStatus", function()
    local status = core.get_status()
    
    print("=== Configuration Status ===")
    print("Project type: " .. status.project_type)
    
    print("\nAvailable tools:")
    for tool, available in pairs(status.available_tools) do
      local icon = available and "✅" or "❌"
      print(string.format("  %s %s", icon, tool))
    end
    
    print("\nDeprecated tools (should be removed):")
    for tool, available in pairs(status.deprecated_tools) do
      if available then
        print(string.format("  ⚠️  %s (still installed)", tool))
      else
        print(string.format("  ✅ %s (correctly removed)", tool))
      end
    end
  end, { desc = "Show configuration status" })
end

-- ============================================================================
-- KEYMAPS
-- ============================================================================

function M.setup_keymaps()
  -- Leader key
  vim.g.mapleader = " "
  vim.g.maplocalleader = " "

  -- Basic navigation
  core.create_keymap("n", "<C-h>", "<C-w>h", { desc = "Go to left window" })
  core.create_keymap("n", "<C-j>", "<C-w>j", { desc = "Go to lower window" })
  core.create_keymap("n", "<C-k>", "<C-w>k", { desc = "Go to upper window" })
  core.create_keymap("n", "<C-l>", "<C-w>l", { desc = "Go to right window" })

  -- Buffer management
  core.create_keymap("n", "<S-h>", "<cmd>bprevious<cr>", { desc = "Prev buffer" })
  core.create_keymap("n", "<S-l>", "<cmd>bnext<cr>", { desc = "Next buffer" })
  core.create_keymap("n", "<leader>bd", "<cmd>bdelete<cr>", { desc = "Delete buffer" })

  -- File operations
  core.create_keymap("n", "<leader>w", "<cmd>w<cr>", { desc = "Save file" })
  core.create_keymap("n", "<leader>q", "<cmd>q<cr>", { desc = "Quit" })
  core.create_keymap("n", "<leader>Q", "<cmd>qa<cr>", { desc = "Quit all" })

  -- Formatting
  core.create_keymap({ "n", "v" }, "<leader>f", function()
    require("conform").format({ async = true, lsp_fallback = true })
  end, { desc = "Format" })

  -- Development shortcuts
  core.create_keymap("n", "<leader>fd", "<cmd>FormatDiagnose<cr>", { desc = "Format diagnostic" })
  core.create_keymap("n", "<leader>hs", "<cmd>HealthStatus<cr>", { desc = "Health status" })
  core.create_keymap("n", "<leader>ms", "<cmd>MasonStatus<cr>", { desc = "Mason status" })

  -- Clear search highlighting
  core.create_keymap("n", "<Esc>", "<cmd>nohlsearch<cr>", { desc = "Clear search highlight" })

  -- Better indenting
  core.create_keymap("v", "<", "<gv", { desc = "Indent left" })
  core.create_keymap("v", ">", ">gv", { desc = "Indent right" })

  -- Move lines
  core.create_keymap("n", "<A-j>", "<cmd>m .+1<cr>==", { desc = "Move line down" })
  core.create_keymap("n", "<A-k>", "<cmd>m .-2<cr>==", { desc = "Move line up" })
  core.create_keymap("i", "<A-j>", "<esc><cmd>m .+1<cr>==gi", { desc = "Move line down" })
  core.create_keymap("i", "<A-k>", "<esc><cmd>m .-2<cr>==gi", { desc = "Move line up" })
  core.create_keymap("v", "<A-j>", ":m '>+1<cr>gv=gv", { desc = "Move selection down" })
  core.create_keymap("v", "<A-k>", ":m '<-2<cr>gv=gv", { desc = "Move selection up" })
end

-- ============================================================================
-- SETUP FUNCTION
-- ============================================================================

function M.setup()
  -- Setup all commands
  M.python_commands()
  M.frontend_commands()
  M.formatter_commands()
  M.mason_commands()
  M.health_commands()
  
  -- Setup keymaps
  M.setup_keymaps()
  
  core.notify("Commands and keymaps loaded", vim.log.levels.INFO)
end

return M
