-- Options are automatically loaded before lazy.nvim startup
-- Default options that are always set: https://github.com/LazyVim/LazyVim/blob/main/lua/lazyvim/config/options.lua
-- Add any additional options here

-- Leader keys (set early to ensure plugins use correct leaders)
vim.g.mapleader = " "
vim.g.maplocalleader = " "

-- Snacks animation
vim.g.snacks_animate = true
vim.g.snacks_animate_duration = 1000 -- Duration in milliseconds for the animation

-- Formatting configuration
vim.g.autoformat = true -- Enable autoformat by default (LazyVim standard)
-- vim.g.disable_autoformat = true -- Uncomment to disable autoformat globally

-- Performance optimizations
vim.opt.updatetime = 250 -- Faster completion (default is 4000ms)
vim.opt.timeoutlen = 300 -- Faster which-key popup

-- Split behavior (fix for terminal splits)
vim.opt.splitbelow = true  -- Horizontal splits go below current window
vim.opt.splitright = true  -- Vertical splits go to the right of current window

-- Better editing experience
vim.opt.scrolloff = 8 -- Keep 8 lines above/below cursor
vim.opt.sidescrolloff = 8 -- Keep 8 columns left/right of cursor
vim.opt.wrap = false -- Don't wrap lines
vim.opt.linebreak = true -- Break lines at word boundaries when wrap is on

-- Better search
vim.opt.ignorecase = true -- Ignore case in search
vim.opt.smartcase = true -- Override ignorecase if search contains uppercase

-- Better indentation
vim.opt.expandtab = true -- Use spaces instead of tabs
vim.opt.shiftwidth = 2 -- Size of an indent
vim.opt.tabstop = 2 -- Number of spaces tabs count for
vim.opt.softtabstop = 2 -- Number of spaces tabs count for in insert mode

-- Better splits
vim.opt.splitbelow = true -- Put new windows below current
vim.opt.splitright = true -- Put new windows right of current

-- Backup and swap file settings
vim.opt.backup = false -- Don't create backup files
vim.opt.writebackup = false -- Don't create backup before overwriting file
vim.opt.swapfile = false -- Don't create swap files (optional, but recommended with version control)

-- WSL2-specific optimizations
if vim.fn.has("wsl") == 1 then
  -- WSL2 clipboard integration
  vim.g.clipboard = {
    name = "WslClipboard",
    copy = {
      ["+"] = "clip.exe",
      ["*"] = "clip.exe",
    },
    paste = {
      ["+"] = 'powershell.exe -c [Console]::Out.Write($(Get-Clipboard -Raw).tostring().replace("`r", ""))',
      ["*"] = 'powershell.exe -c [Console]::Out.Write($(Get-Clipboard -Raw).tostring().replace("`r", ""))',
    },
    cache_enabled = 0,
  }

  -- WSL2 performance optimizations
  vim.opt.fsync = false -- Faster file writes on WSL2
  vim.opt.directory = vim.fn.expand("~/.cache/nvim/swap//") -- Centralized swap directory
  vim.opt.undodir = vim.fn.expand("~/.cache/nvim/undo//") -- Centralized undo directory
  vim.opt.undofile = true -- Persistent undo

  -- Create cache directories if they don't exist
  vim.fn.mkdir(vim.fn.expand("~/.cache/nvim/swap"), "p")
  vim.fn.mkdir(vim.fn.expand("~/.cache/nvim/undo"), "p")
end

-- Duplicate WSL2 configuration removed (already configured above)

-- Whitespace and trimming configuration
vim.opt.list = false -- Don't show by default (can be toggled)
vim.opt.listchars = {
  trail = "·",        -- Show trailing spaces as dots
  tab = "→ ",         -- Show tabs as arrows
  nbsp = "␣",         -- Show non-breaking spaces
  extends = "⟩",      -- Show when line extends beyond screen
  precedes = "⟨",     -- Show when line precedes screen
  eol = "¬",          -- Show end of line (when list is enabled)
}

-- Auto-trim settings
vim.g.auto_trim_enabled = false  -- Disable auto-trim by default

-- Python development optimizations
vim.g.python_recommended_style = 0  -- Disable vim's python style (we use our own)
vim.g.python_highlight_all = 1      -- Enable all Python syntax highlighting

-- Modern Python development settings (set in lazy.lua to avoid loading issues)
-- vim.g.python3_host_prog is set in lazy.lua

-- Python settings moved to autocmds.lua to avoid duplication

-- JavaScript/TypeScript whitespace settings
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "javascript", "typescript", "javascriptreact", "typescriptreact" },
  callback = function()
    -- JavaScript prefers 2 spaces
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.softtabstop = 2
    vim.opt_local.expandtab = true
  end,
})

-- HTML/CSS whitespace settings
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "html", "css", "scss", "sass", "less" },
  callback = function()
    -- Web files prefer 2 spaces
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.softtabstop = 2
    vim.opt_local.expandtab = true
  end,
})

-- YAML files (careful with indentation)
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "yaml", "yml" },
  callback = function()
    -- YAML requires 2 spaces and is very sensitive to whitespace
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.softtabstop = 2
    vim.opt_local.expandtab = true

    -- Always show whitespace for YAML
    vim.opt_local.list = true
    vim.opt_local.listchars = {
      trail = "·",
      tab = "→ ",
      nbsp = "␣",
    }
  end,
})

-- Markdown files
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "markdown", "md" },
  callback = function()
    -- Markdown can be sensitive to trailing spaces (line breaks)
    vim.opt_local.tabstop = 2
    vim.opt_local.shiftwidth = 2
    vim.opt_local.softtabstop = 2
    vim.opt_local.expandtab = true

    -- Show trailing spaces in markdown (they have meaning)
    vim.opt_local.list = true
    vim.opt_local.listchars = {
      trail = "·",
      tab = "→ ",
    }
  end,
})

-- Security settings
vim.opt.modeline = false        -- Disable modeline for security
vim.opt.exrc = false           -- Disable reading .vimrc/.exrc in current directory
vim.opt.secure = true         -- Secure mode for exrc

-- Performance optimizations
vim.opt.lazyredraw = true      -- Don't redraw during macros
vim.opt.regexpengine = 1       -- Use old regexp engine (faster for some patterns)
vim.opt.synmaxcol = 200        -- Don't syntax highlight long lines

-- Better completion
vim.opt.completeopt = { "menu", "menuone", "noselect" }
vim.opt.pumheight = 10         -- Limit popup menu height

-- File encoding
vim.opt.encoding = "utf-8"
vim.opt.fileencoding = "utf-8"
