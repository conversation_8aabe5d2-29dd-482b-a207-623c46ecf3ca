-- Set Python provider with fallback
local function get_python_provider()
  local providers = {
    vim.fn.expand('~/.pyenv/versions/neovim-env/bin/python'),
    vim.fn.expand('~/.pyenv/versions/neovim/bin/python'),
    vim.fn.exepath('python3'),
    vim.fn.exepath('python'),
    '/usr/bin/python3',
  }

  for _, provider in ipairs(providers) do
    if vim.fn.executable(provider) == 1 then
      return provider
    end
  end

  return 'python3'  -- fallback
end

vim.g.python3_host_prog = get_python_provider()

-- Bootstrap lazy.nvim
local lazypath = vim.fn.stdpath("data") .. "/lazy/lazy.nvim"
if not vim.loop.fs_stat(lazypath) then
  vim.fn.system({
    "git",
    "clone",
    "--filter=blob:none",
    "https://github.com/folke/lazy.nvim.git",
    "--branch=stable",
    lazypath,
  })
end
vim.opt.rtp:prepend(vim.env.LAZY or lazypath)

-- Plugin specifications - Streamlined and consolidated
local plugins = {
  -- Core LazyVim (minimal)
  { "LazyVim/LazyVim", import = "lazyvim.plugins" },

  -- Core functionality (consolidated)
  { import = "core.plugins" },      -- Essential editor plugins
  { import = "core.formatters" },   -- Modern formatters only
  { import = "core.mason" },        -- Package management

  -- Language support (minimal, essential only)
  { import = "lazyvim.plugins.extras.lang.typescript" },
  { import = "lazyvim.plugins.extras.lang.json" },
  { import = "lazyvim.plugins.extras.lang.yaml" },

  -- Development tools (modern only)
  { import = "lazyvim.plugins.extras.formatting.biome" },

  -- UI components (keep existing)
  { import = "plugins.mini-starter" },
  { import = "plugins.colorscheme" },
}

-- Lazy.nvim configuration (optimized)
require("lazy").setup({
  spec = plugins,
  defaults = {
    lazy = true,  -- Enable lazy loading by default
    version = false,
  },
  install = {
    colorscheme = { "catppuccin", "tokyonight", "habamax" }
  },
  checker = {
    enabled = true,
    frequency = 3600,  -- Check once per hour instead of constantly
  },
  change_detection = {
    enabled = true,
    notify = false,  -- Reduce notification noise
  },

  -- Configure rocks/luarocks support
  rocks = {
    enabled = true,
    root = vim.fn.stdpath("data") .. "/lazy-rocks",
    server = "https://nvim-neorocks.github.io/rocks-binaries/",
    -- Use hererocks to manage Lua 5.1 for Neovim plugins
    hererocks = true,
  },

  performance = {
    cache = {
      enabled = true,
    },
    reset_packpath = true,
    rtp = {
      reset = true,
      paths = {},
      disabled_plugins = {
        "gzip",
        "tarPlugin",
        "tohtml",
        "tutor",
        "zipPlugin",
        "netrw",
        "netrwPlugin",
        "netrwSettings",
        "netrwFileHandlers",
        "matchit",
        "matchparen",
        "2html_plugin",
        "getscript",
        "getscriptPlugin",
        "logipat",
        "rrhelper",
        "spellfile_plugin",
        "vimball",
        "vimballPlugin",
      },
    },
  },
})

-- Load essential commands and keymaps after plugins are loaded
vim.defer_fn(function()
  require("core.commands").setup()
end, 100)
