-- Simplified Health Check Module
local core = require("core.config")
local M = {}

-- Main health check function (required by Neovim health system)
function M.check()
  vim.health.start("Python Fullstack Development Configuration")

  -- Check Neovim version
  local nvim_version = vim.version()
  if nvim_version.major >= 0 and nvim_version.minor >= 9 then
    vim.health.ok("Neovim version: " .. nvim_version.major .. "." .. nvim_version.minor .. "." .. nvim_version.patch)
  else
    vim.health.error("Neovim version too old. Requires 0.9+")
  end

  -- Get configuration status
  local status = core.get_status()

  -- Check essential tools
  vim.health.start("Essential Development Tools")

  for tool, available in pairs(status.available_tools) do
    if available then
      vim.health.ok(tool .. " is available")
    else
      vim.health.error(tool .. " is missing")
    end
  end

  -- Check deprecated tools
  vim.health.start("Deprecated Tools Check")

  for tool, available in pairs(status.deprecated_tools) do
    if available then
      vim.health.warn(tool .. " is still installed (should be removed)")
    else
      vim.health.ok(tool .. " correctly removed")
    end
  end

  -- Check key plugins
  vim.health.start("Key Plugins")

  local essential_plugins = {
    { name = "conform", desc = "Formatter plugin" },
    { name = "mason", desc = "Package manager" },
    { name = "nvim-treesitter", desc = "Syntax highlighting" },
    { name = "lspconfig", desc = "LSP configuration" },
  }

  for _, plugin in ipairs(essential_plugins) do
    if core.is_plugin_loaded(plugin.name) then
      vim.health.ok(plugin.name .. " is loaded (" .. plugin.desc .. ")")
    else
      vim.health.error(plugin.name .. " failed to load (" .. plugin.desc .. ")")
    end
  end

  -- Check Mason packages
  vim.health.start("Mason Packages")

  local mason_registry = core.safe_require("mason-registry")
  if mason_registry then
    for _, tool in ipairs(core.constants.mason_packages) do
      local pkg = mason_registry.get_package(tool)
      if pkg:is_installed() then
        vim.health.ok(tool .. " is installed")
      else
        vim.health.warn(tool .. " is not installed")
      end
    end
  else
    vim.health.error("Mason registry not available")
  end

  -- Project detection
  vim.health.start("Project Detection")
  vim.health.info("Detected project type: " .. status.project_type)

  -- Configuration summary
  vim.health.start("Configuration Summary")
  vim.health.ok("Modern formatter configuration: Ruff (Python), Biome (JS/TS), Stylua (Lua)")
  vim.health.ok("No deprecated tools (black, isort, prettier, eslint)")
  vim.health.info("Use :FormatDiagnose for detailed formatter status")
  vim.health.info("Use :HealthStatus for quick status check")

  -- Check Python environment
  vim.health.start("Python Environment")
  local python_provider = vim.g.python3_host_prog
  if vim.fn.executable(python_provider) == 1 then
    vim.health.ok("Python provider: " .. python_provider)
  else
    vim.health.error("Python provider not found: " .. python_provider)
  end

  -- Check Ruff installation
  if vim.fn.executable("ruff") == 1 then
    local ruff_version = vim.fn.system("ruff --version"):gsub("\n", "")
    vim.health.ok("Ruff installed: " .. ruff_version)
  else
    vim.health.error("Ruff not found. Install with: pip install ruff")
  end
end

-- Legacy function for backwards compatibility
function M.check_dev_tools()
  vim.health.info("Use M.check() for comprehensive health check")
end

-- Legacy functions for backwards compatibility
function M.check_wsl2()
  vim.health.info("Use M.check() for comprehensive health check")
end

function M.check_project()
  vim.health.info("Use M.check() for comprehensive health check")
end

function M.check_performance()
  vim.health.info("Use M.check() for comprehensive health check")
end

function M.check_python()
  vim.health.info("Use M.check() for comprehensive health check")
end

-- Run all health checks (alias for backwards compatibility)
function M.check_all()
  M.check()
end

-- Create user command for health checks
vim.api.nvim_create_user_command("HealthAll", M.check_all, { desc = "Run comprehensive health check" })

return M
