-- Custom mini.starter configuration to fix startup issues
return {
  -- Disable Snacks dashboard to avoid conflicts
  {
    "folke/snacks.nvim",
    opts = {
      dashboard = { enabled = false },
    },
  },

  {
    "echa<PERSON><PERSON>ski/mini.starter",
    version = false, -- wait till new 0.7.0 release to put it back on semver
    event = "VimEnter",
  opts = function()
    local logo = table.concat({
      "            ██╗      █████╗ ███████╗██╗   ██╗██╗   ██╗██╗███╗   ███╗          Z",
      "            ██║     ██╔══██╗╚══███╔╝╚██╗ ██╔╝██║   ██║██║████╗ ████║      Z    ",
      "            ██║     ███████║  ███╔╝  ╚████╔╝ ██║   ██║██║██╔████╔██║   z       ",
      "            ██║     ██╔══██║ ███╔╝    ╚██╔╝  ╚██╗ ██╔╝██║██║╚██╔╝██║ z         ",
      "            ███████╗██║  ██║███████╗   ██║    ╚████╔╝ ██║██║ ╚═╝ ██║           ",
      "            ╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝     ╚═══╝  ╚═╝╚═╝     ╚═╝           ",
      "",
      "                     🐍 Python Fullstack • ⚡ Ruff + Biome • 🚀 LazyVim",
      "",
    }, "\n")

    local pad = string.rep(" ", 22)
    local new_section = function(name, action, section)
      return { name = name, action = action, section = pad .. section }
    end

    local starter = require("mini.starter")
    --stylua: ignore
    local config = {
      evaluate_single = true,
      header = logo,
      items = {
        new_section("Find file",           LazyVim.pick(),                        "Telescope"),
        new_section("New file",            "ene | startinsert",                   "Built-in"),
        new_section("Recent files",        LazyVim.pick("oldfiles"),              "Telescope"),
        new_section("Find text",           LazyVim.pick("live_grep"),             "Telescope"),
        new_section("Config",              LazyVim.pick.config_files(),           "Config"),
        new_section("Restore session",     [[lua require("persistence").load()]], "Session"),
        new_section("Lazy Extras",         "LazyExtras",                          "Config"),
        new_section("Lazy",                "Lazy",                                "Config"),
        new_section("Quit",                "qa",                                  "Built-in"),
        -- LazyVim Management
        new_section("Mason",               "Mason",                               "LazyVim"),
        new_section("Lazy Health",         "checkhealth lazy",                    "LazyVim"),
        new_section("Lazy Profile",        "Lazy profile",                        "LazyVim"),
        new_section("Lazy Clean",          "Lazy clean",                          "LazyVim"),
        new_section("Lazy Update",         "Lazy update",                         "LazyVim"),
        new_section("Lazy Sync",           "Lazy sync",                           "LazyVim"),
        -- Git & Project
        new_section("LazyGit",             "LazyGit",                             "Git"),
        new_section("Find in project",     LazyVim.pick("files"),                 "Project"),
        new_section("Browse files",        "Oil",                                 "Project"),
        -- Development Tools
        new_section("Format Diagnostic",   "FormatDiagnose",                      "Dev Tools"),
        new_section("Health Check",        "checkhealth config",                  "Dev Tools"),
        new_section("Ruff Format",         "RuffFormat",                          "Python"),
        new_section("Ruff Check",          "RuffCheck",                           "Python"),
        new_section("Mason Status",        "MasonStatus",                         "Dev Tools"),
        new_section("Terminal",            "terminal",                            "Dev Tools"),
      },
      content_hooks = {
        starter.gen_hook.adding_bullet(pad .. "░ ", false),
        starter.gen_hook.aligning("center", "center"),
      },
    }
    return config
  end,
  config = function(_, config)
    -- close Lazy and re-open when starter is ready
    if vim.o.filetype == "lazy" then
      vim.cmd.close()
      vim.api.nvim_create_autocmd("User", {
        pattern = "MiniStarterOpened",
        callback = function()
          require("lazy").show()
        end,
      })
    end

    local starter = require("mini.starter")
    starter.setup(config)

    vim.api.nvim_create_autocmd("User", {
      pattern = "LazyVimStarted",
      callback = function(ev)
        local stats = require("lazy").stats()
        local ms = (math.floor(stats.startuptime * 100 + 0.5) / 100)
        local pad_footer = string.rep(" ", 8)
        starter.config.footer = pad_footer .. "⚡ Neovim loaded " .. stats.loaded .. "/" .. stats.count .. " plugins in " .. ms .. "ms"
        -- INFO: should be `pcall(starter.refresh)` but on 0.7.0 `refresh()` is `update()`
        if vim.version().minor >= 7 then
          pcall(starter.refresh)
        else
          pcall(starter.update)
        end
      end,
    })
  end,
  },
}
