return {
  -- <PERSON><PERSON><PERSON>n Mocha - Best theme ever for Python
  {
    "catppuccin/nvim",
    name = "catppuccin",
    lazy = true,
    priority = 1000,
    config = function()
      require("catppuccin").setup({
        flavour = "mocha", -- latte, frappe, macchiato, mocha (better for <PERSON>)
        background = { -- :h background
          light = "latte",
          dark = "mocha",
        },
        transparent_background = false, -- disables setting the background color.
        show_end_of_buffer = false, -- shows the '~' characters after the end of buffers
        term_colors = true, -- sets terminal colors (e.g. `g:terminal_color_0`)
        dim_inactive = {
          enabled = false, -- dims the background color of inactive window
          shade = "dark",
          percentage = 0.15, -- percentage of the shade to apply to the inactive window
        },
        no_italic = false, -- Force no italic
        no_bold = false, -- Force no bold
        no_underline = false, -- Force no underline
        styles = { -- Handles the styles of general hi groups (see `:h highlight-args`):
          comments = { "italic" }, -- Change the style of comments
          conditionals = { "italic" },
          loops = {},
          functions = { "bold" }, -- Bold functions for Python
          keywords = { "italic" }, -- Italic keywords
          strings = {},
          variables = {},
          numbers = {},
          booleans = { "bold" }, -- Bold booleans
          properties = {},
          types = { "bold" }, -- Bold types
          operators = {},
        },
        color_overrides = {},
        custom_highlights = function(colors)
          return {
            -- Basic Python syntax highlighting (safe)
            ["@keyword.python"] = { fg = colors.mauve, italic = true },
            ["@function.python"] = { fg = colors.blue, bold = true },
            ["@string.python"] = { fg = colors.green },
            ["@decorator.python"] = { fg = colors.yellow, bold = true },

            -- LSP diagnostics
            DiagnosticError = { fg = colors.red },
            DiagnosticWarn = { fg = colors.yellow },
            DiagnosticInfo = { fg = colors.sky },
            DiagnosticHint = { fg = colors.teal },
          }
        end,
        integrations = {
          cmp = true,
          gitsigns = true,
          nvimtree = true,
          neotree = true,
          treesitter = true,
          notify = true,
          mini = {
            enabled = true,
            indentscope_color = "",
          },
          telescope = {
            enabled = true,
          },
          illuminate = {
            enabled = true,
            lsp = false
          },
          which_key = true,
          indent_blankline = {
            enabled = true,
            scope_color = "",
            colored_indent_levels = false,
          },
          native_lsp = {
            enabled = true,
            virtual_text = {
              errors = { "italic" },
              hints = { "italic" },
              warnings = { "italic" },
              information = { "italic" },
            },
            underlines = {
              errors = { "undercurl" },
              hints = { "undercurl" },
              warnings = { "undercurl" },
              information = { "undercurl" },
            },
            inlay_hints = {
              background = true,
            },
          },
          navic = {
            enabled = false,
            custom_bg = "NONE",
          },
          neotest = true,
          noice = true,
          semantic_tokens = true,
          lsp_trouble = true,
          mason = true,
          dap = true,
          dap_ui = true,
          barbecue = {
            dim_dirname = true,
            bold_basename = true,
            dim_context = false,
            alt_background = false,
          },
          markdown = true,
          leap = true,
          alpha = true,
          dashboard = true,
          flash = true,
          hop = true,
          gitgutter = false,
          fidget = true,
          dropbar = {
            enabled = true,
            color_mode = true, -- Enable colors for Python symbols
          },
          overseer = true,
          rainbow_delimiters = true,
        },
      })
    end,
  },

  -- Tokyo Night - Modern alternative for Python
  {
    "folke/tokyonight.nvim",
    lazy = true,
    priority = 1000,
    opts = {
      style = "night", -- storm, moon, night, day
      light_style = "day",
      transparent = false,
      terminal_colors = true,
      styles = {
        comments = { italic = true },
        keywords = { italic = true },
        functions = { bold = true },
        variables = {},
        sidebars = "dark",
        floats = "dark",
      },
      sidebars = { "qf", "help", "terminal", "neo-tree" },
      day_brightness = 0.3,
      hide_inactive_statusline = false,
      dim_inactive = false,
      lualine_bold = false,
      on_colors = function(colors)
        -- Python-optimized colors
        colors.blue = "#7aa2f7"      -- Functions, classes
        colors.cyan = "#7dcfff"      -- Imports, decorators
        colors.green = "#9ece6a"     -- Strings, success
        colors.magenta = "#bb9af7"   -- Keywords, control flow
        colors.orange = "#ff9e64"    -- Numbers, constants
        colors.purple = "#9d7cd8"    -- Types, built-ins
        colors.red = "#f7768e"       -- Errors, exceptions
        colors.yellow = "#e0af68"    -- Warnings, docstrings
      end,
      on_highlights = function(highlights, colors)
        -- Python-specific syntax highlighting
        highlights["@keyword.python"] = { fg = colors.magenta, italic = true }
        highlights["@function.python"] = { fg = colors.blue, bold = true }
        highlights["@function.builtin.python"] = { fg = colors.purple, bold = true }
        highlights["@string.python"] = { fg = colors.green }
        highlights["@string.documentation.python"] = { fg = colors.yellow, italic = true }
        highlights["@decorator.python"] = { fg = colors.cyan, bold = true }
        highlights["@variable.builtin.python"] = { fg = colors.red, italic = true }
        highlights["@constant.python"] = { fg = colors.orange, bold = true }
        highlights["@type.python"] = { fg = colors.purple, bold = true }

        -- Django/FastAPI
        highlights["@keyword.django"] = { fg = colors.green, bold = true }
        highlights["@function.django"] = { fg = colors.blue, bold = true }
        highlights["@decorator.fastapi"] = { fg = colors.cyan, bold = true }

        -- JavaScript/TypeScript (full stack)
        highlights["@keyword.javascript"] = { fg = colors.magenta, italic = true }
        highlights["@function.javascript"] = { fg = colors.blue, bold = true }
        highlights["@keyword.typescript"] = { fg = colors.magenta, italic = true }
        highlights["@function.typescript"] = { fg = colors.blue, bold = true }
      end,
    },
  },

  -- Gruvbox - Classic programmer theme with Python optimizations
  {
    "ellisonleao/gruvbox.nvim",
    lazy = true,
    opts = {
      terminal_colors = true,
      undercurl = true,
      underline = true,
      bold = true,
      italic = {
        strings = true,
        emphasis = true,
        comments = true,
        operators = false,
        folds = true,
      },
      strikethrough = true,
      invert_selection = false,
      invert_signs = false,
      invert_tabline = false,
      invert_intend_guides = false,
      inverse = true,
      contrast = "hard", -- Better contrast for Python code
      palette_overrides = {
        -- Python-friendly color palette
        bright_green = "#b8bb26",    -- Strings
        bright_yellow = "#fabd2f",   -- Keywords
        bright_blue = "#83a598",     -- Functions
        bright_purple = "#d3869b",   -- Types
        bright_red = "#fb4934",      -- Errors
        bright_orange = "#fe8019",   -- Numbers
        bright_aqua = "#8ec07c",     -- Constants
      },
      overrides = {
        -- Python syntax optimizations
        ["@keyword.python"] = { fg = "#d3869b", italic = true },
        ["@function.python"] = { fg = "#83a598", bold = true },
        ["@function.builtin.python"] = { fg = "#8ec07c", bold = true },
        ["@string.python"] = { fg = "#b8bb26" },
        ["@string.documentation.python"] = { fg = "#b8bb26", italic = true },
        ["@decorator.python"] = { fg = "#fabd2f", bold = true },
        ["@variable.builtin.python"] = { fg = "#fb4934", italic = true },
        ["@constant.python"] = { fg = "#fe8019", bold = true },
        ["@type.python"] = { fg = "#d3869b", bold = true },

        -- Enhanced LSP integration
        DiagnosticError = { fg = "#fb4934" },
        DiagnosticWarn = { fg = "#fabd2f" },
        DiagnosticInfo = { fg = "#83a598" },
        DiagnosticHint = { fg = "#8ec07c" },
      },
    },
  },

  -- Configure LazyVim to load Catppuccin Mocha (optimized for Python)
  {
    "LazyVim/LazyVim",
    opts = {
      colorscheme = "catppuccin-mocha",
      -- Alternative themes for Python Full Stack:
      -- colorscheme = "catppuccin-frappe",  -- Lighter alternative
      -- colorscheme = "tokyonight-night",   -- Modern alternative
      -- colorscheme = "gruvbox",            -- Classic alternative
    },
  },
  {
    "rachartier/tiny-glimmer.nvim",
    event = "TextYankPost",
    cmd = "TinyGlimmer",
    opts = {
      -- your configuration
    },
  },
}
