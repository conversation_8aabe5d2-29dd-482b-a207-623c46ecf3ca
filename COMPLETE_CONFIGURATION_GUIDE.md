# 📚 Complete Neovim Configuration Guide - Python Fullstack Development

## 🎯 **OVERVIEW**

This Neovim configuration is optimized for **Python fullstack web development** with modern, fast tools:

- **Ruff**: Python formatting, import organization, and linting with project-aware detection
- **Biome**: JavaScript/TypeScript/JSON formatting and linting (complete Prettier replacement)
- **Stylua**: Lua formatting
- **LSP Fallback**: Native formatting for HTML/CSS/YAML/Markdown

## 🐍 **RUFF CONFIGURATION - Python Project-Aware**

### **Project Detection System:**

Ruff automatically detects your project type and applies appropriate rules:

#### **Django Projects:**
- **Detection**: Presence of `manage.py` or `settings.py`
- **Rules**: Django-specific linting (DJ rules), model conventions, admin best practices
- **Line Length**: 88 characters
- **Ignored**: `E501` (line too long), `N806` (non-lowercase variable), `A003` (builtin shadowing)

#### **FastAPI Projects:**
- **Detection**: `main.py` with FastAPI imports
- **Rules**: Async/await best practices (ASYNC rules), type annotations (ANN rules)
- **Line Length**: 88 characters
- **Ignored**: `E501`, `ANN101` (self annotation), `ANN102` (cls annotation), `ANN204` (return annotation)

#### **General Python:**
- **Detection**: Default for other Python projects
- **Rules**: Standard Python best practices
- **Line Length**: 88 characters
- **Ignored**: `E501`, `T201` (print statements)

### **Ruff Commands:**

```vim
:RuffFormat     " Format Python file (project-aware)
:RuffImports    " Organize imports (project-aware)
:RuffAll        " Format + organize imports (project-aware)
:RuffLint       " Lint with project-specific rules
:RuffConfig     " Show current project configuration
:PythonWorkflow " Complete workflow (format + organize + lint)
```

### **Ruff Keybindings:**

```vim
<leader>rf      " RuffFormat
<leader>ri      " RuffImports
<leader>ra      " RuffAll
<leader>rl      " RuffLint
<leader>rc      " RuffConfig
<leader>pw      " PythonWorkflow
```

### **Ruff Configuration File (`ruff.toml`):**

```toml
# Optimized for Python fullstack web development
target-version = "py311"
line-length = 88
fix = true
show-fixes = true

[lint]
select = [
    "F",    # Pyflakes
    "E", "W", # pycodestyle
    "I",    # isort
    "N",    # pep8-naming
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "SIM",  # flake8-simplify
    "C4",   # flake8-comprehensions
    "PIE",  # flake8-pie
    "RET",  # flake8-return
    "DJ",   # flake8-django (Django projects)
    "PT",   # flake8-pytest-style
    "G",    # flake8-logging-format
    "Q",    # flake8-quotes
    "ANN",  # flake8-annotations (FastAPI projects)
    "ASYNC", # flake8-async (FastAPI projects)
    "S",    # flake8-bandit (security)
]

# Project-specific ignores
ignore = [
    "E501",  # Line too long (handled by formatter)
    "T201",  # Print statements (allowed in development)
]

[format]
quote-style = "double"
indent-style = "space"
skip-string-normalization = false
line-ending = "auto"
```

## ⚡ **BIOME CONFIGURATION - Complete Prettier Replacement**

### **Biome Handles:**
- **JavaScript** (.js, .jsx)
- **TypeScript** (.ts, .tsx)
- **JSON** (.json)
- **React/Vue Components**

### **Biome Commands:**

```vim
:BiomeFormat    " Format JS/TS/JSON with Biome
:BiomeLint      " Lint with Biome
:BiomeCheck     " Format + lint with Biome
:JSWorkflow     " Complete JS/TS workflow (format + lint)
```

### **Biome Keybindings:**

```vim
<leader>bf      " BiomeFormat
<leader>bl      " BiomeLint
<leader>bc      " BiomeCheck
<leader>jw      " JSWorkflow
```

### **Biome Configuration (`biome.json`):**

```json
{
  "$schema": "https://biomejs.dev/schemas/2.0.0/schema.json",
  "formatter": {
    "enabled": true,
    "indentWidth": 2,
    "lineWidth": 100
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true
    }
  },
  "javascript": {
    "formatter": {
      "semicolons": "always",
      "trailingCommas": "es5",
      "quoteStyle": "double"
    }
  }
}
```

### **Performance Benefits:**
- **10-100x faster** than Prettier
- **Single tool** for formatting and linting
- **Rust-based** for maximum performance
- **Zero configuration** needed for basic usage

## 🔧 **UNIVERSAL SMART FORMATTING**

### **Smart Format Command:**

The `:Format` command automatically detects file type and uses the appropriate formatter:

```vim
:Format         " Smart format command
<leader>f       " Smart format keybinding
```

### **File Type Detection:**

```lua
-- Smart formatting logic
if filetype == "python" then
    -- Use Ruff for Python
    require("config.ruff-manual").format_python()
elseif filetype in ["javascript", "typescript", "javascriptreact", "typescriptreact", "json"] then
    -- Use Biome for JS/TS/JSON
    conform.format({ formatters = { "biome" }, async = true, lsp_fallback = true })
elseif filetype == "lua" then
    -- Use Stylua for Lua
    conform.format({ formatters = { "stylua" }, async = true, lsp_fallback = true })
else
    -- Use LSP formatting for everything else
    vim.lsp.buf.format({ async = true })
end
```

## 📁 **COMPLETE FILE TYPE HANDLING STRATEGY**

| File Type | Formatter | Speed | Features |
|-----------|-----------|-------|----------|
| **Python** (.py) | Ruff | ⚡⚡⚡ | Format + Import Organization + Linting + Project Detection |
| **JavaScript** (.js, .jsx) | Biome | ⚡⚡⚡ | Format + Linting + Import Organization |
| **TypeScript** (.ts, .tsx) | Biome | ⚡⚡⚡ | Format + Linting + Import Organization |
| **JSON** (.json) | Biome | ⚡⚡⚡ | Format + Validation |
| **Lua** (.lua) | Stylua | ⚡⚡ | Format |
| **HTML** (.html) | LSP | ⚡ | Native Editor Formatting |
| **CSS** (.css, .scss) | LSP | ⚡ | Native Editor Formatting |
| **YAML** (.yml, .yaml) | LSP | ⚡ | Native Editor Formatting |
| **Markdown** (.md) | LSP | ⚡ | Native Editor Formatting |

## 🔄 **DEVELOPMENT WORKFLOWS**

### **Python Development Workflow:**

```vim
<leader>pw      " Complete Python workflow
:PythonWorkflow " Command version

# Executes:
# 1. Format code with Ruff
# 2. Organize imports (project-aware)
# 3. Lint with project-specific rules
```

### **JavaScript/TypeScript Development Workflow:**

```vim
<leader>jw      " Complete JS/TS workflow
:JSWorkflow     " Command version

# Executes:
# 1. Format code with Biome
# 2. Lint with Biome
# 3. Organize imports automatically
```

### **Universal Development Workflow:**

```vim
<leader>dw      " Universal workflow
:DevWorkflow    " Command version

# Auto-detects file type and runs appropriate workflow:
# - Python files → PythonWorkflow
# - JS/TS files → JSWorkflow
# - Other files → Smart format
```

## 🛠️ **INSTALLATION & SETUP**

### **Required Tools:**

```bash
# Install Ruff (Python)
pip install ruff

# Install Biome (JavaScript/TypeScript)
npm install -g @biomejs/biome

# Install Stylua (Lua)
cargo install stylua
# or
brew install stylua
```

### **Mason Packages:**

The following packages are automatically installed via Mason:

```lua
{
  -- Python ecosystem
  "ruff",                    -- Python formatter/linter
  "pyright",                 -- Python LSP
  
  -- Frontend ecosystem
  "typescript-language-server", -- TypeScript LSP
  "biome",                   -- JS/TS formatter/linter
  
  -- Lua ecosystem
  "stylua",                  -- Lua formatter
  "lua-language-server",     -- Lua LSP
}
```

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **Ruff not working:**
1. Check installation: `ruff --version`
2. Install if missing: `pip install ruff`
3. Check project detection: `:RuffConfig`
4. Restart LSP: `<leader>lr`

#### **Biome not working:**
1. Check installation: `biome --version`
2. Install if missing: `npm install -g @biomejs/biome`
3. Check file type: Biome only supports JS/TS/JSON
4. Use manual command: `:BiomeFormat`

#### **Smart format not working:**
1. Check file type: `:lua print(vim.bo.filetype)`
2. Use specific formatter: `:RuffFormat` or `:BiomeFormat`
3. Check LSP status: `:LspInfo`
4. Reload config: `<leader>lR`

### **Diagnostic Commands:**

```vim
:MasonDiagnose  " Check Mason installation issues
<leader>lr      " Restart LSP
<leader>lR      " Reload Neovim config
:LspInfo        " Check LSP status
:ConformInfo    " Check formatter status
```

## 📈 **PERFORMANCE OPTIMIZATIONS**

### **Speed Improvements:**
- **Ruff**: 10-100x faster than Black + isort + flake8
- **Biome**: 10-100x faster than ESLint + Prettier
- **Native LSP**: Built-in editor formatting
- **Async formatting**: Non-blocking operations

### **Memory Efficiency:**
- **Rust-based tools** (Ruff, Biome) use less memory than Python/Node.js tools
- **Single tools** instead of multiple separate tools
- **LSP fallback** uses native editor resources

## 🎯 **BEST PRACTICES**

### **Daily Usage:**
1. Use `<leader>f` for quick formatting (most common)
2. Use `<leader>pw` for complete Python workflow
3. Use `<leader>jw` for complete JS/TS workflow
4. Use `<leader>dw` for universal workflow

### **Project Setup:**
1. Create `ruff.toml` for Python project-specific rules
2. Create `biome.json` for JS/TS project-specific rules
3. Let project detection handle the rest automatically

### **Performance Tips:**
1. Use async formatting (enabled by default)
2. Format on save is optional (disabled by default)
3. Use specific formatters for large files
4. Restart LSP if formatting becomes slow

## 🎉 **SUMMARY**

This configuration provides:

✅ **Fast, modern formatting** with Ruff and Biome
✅ **Project-aware Python development** with Django/FastAPI detection
✅ **Complete Prettier replacement** with Biome
✅ **Smart file type detection** and appropriate formatter selection
✅ **Comprehensive workflows** for different development scenarios
✅ **LSP fallback** for unsupported file types
✅ **Zero configuration** needed for basic usage
✅ **Professional development experience** optimized for fullstack Python development

**Start using: Open any file and press `<leader>f` for smart formatting!**
