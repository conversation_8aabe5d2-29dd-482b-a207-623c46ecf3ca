# 📚 Complete Neovim Configuration Guide - Python Fullstack Development

## 🎯 **OVERVIEW**

This Neovim configuration is optimized for **Python fullstack web development** with modern, fast tools and intelligent project-aware execution:

- **Ruff**: Python formatting, import organization, and linting with automatic project root detection
- **Biome**: JavaScript/TypeScript/JSON formatting and linting (complete Prettier replacement)
- **Stylua**: Lua formatting
- **blink.cmp**: Modern completion engine (LazyVim default, replaces nvim-cmp)
- **Project-Aware Execution**: All tools automatically find and use project-specific configuration files
- **Working Directory Intelligence**: Commands execute in correct project context regardless of Neovim startup location

## 🐍 **RUFF CONFIGURATION - Python Project-Aware**

### **Project Detection System:**

Ruff automatically detects your project type and applies appropriate rules:

#### **Django Projects:**
- **Detection**: Presence of `manage.py` or `settings.py`
- **Rules**: Django-specific linting (DJ rules), model conventions, admin best practices
- **Line Length**: 88 characters
- **Ignored**: `E501` (line too long), `N806` (non-lowercase variable), `A003` (builtin shadowing)

#### **FastAPI Projects:**
- **Detection**: `main.py` with FastAPI imports
- **Rules**: Async/await best practices (ASYNC rules), type annotations (ANN rules)
- **Line Length**: 88 characters
- **Ignored**: `E501`, `ANN101` (self annotation), `ANN102` (cls annotation), `ANN204` (return annotation)

#### **General Python:**
- **Detection**: Default for other Python projects
- **Rules**: Standard Python best practices
- **Line Length**: 88 characters
- **Ignored**: `E501`, `T201` (print statements)

### **Ruff Commands (Project-Aware Execution):**

All Ruff commands automatically detect the project root and execute in the correct working directory:

```vim
:RuffFormat     " Format entire project (runs in project root)
:RuffCheck      " Check entire project (runs in project root)
:RuffAll        " Format current file + check project (hybrid approach)
:RuffConfig     " Show project detection and configuration details
:PythonWorkflow " Complete workflow (format current file + check project)
```

**Working Directory Intelligence:**
- Commands automatically find project root by looking for `ruff.toml`, `pyproject.toml`, or `.git`
- Execution happens in project root regardless of Neovim startup directory
- Project-specific configuration files are automatically discovered and used
- Clear feedback shows detected project root and configuration file location

### **Ruff Keybindings:**

```vim
<leader>rf      " RuffFormat (project-wide)
<leader>rc      " RuffCheck (project-wide)
<leader>ra      " RuffAll (file + project)
<leader>pw      " PythonWorkflow (complete)
<leader>f       " Smart format current file (uses conform with project context)
```

### **Python Indentation Configuration (4 Spaces - PEP 8):**

**Neovim automatically configures Python files for 4-space indentation:**
```lua
-- Python files use 4 spaces (PEP 8 standard)
vim.api.nvim_create_autocmd("FileType", {
  pattern = { "python" },
  callback = function()
    vim.opt_local.tabstop = 4        -- Tab width
    vim.opt_local.shiftwidth = 4     -- Indent width
    vim.opt_local.softtabstop = 4    -- Soft tab width
    vim.opt_local.expandtab = true   -- Use spaces, not tabs
    vim.opt_local.textwidth = 88     -- Ruff/Black line length
    vim.opt_local.colorcolumn = "89" -- Visual guide at 89 chars
  end,
})
```

**Other file types maintain their specific indentation:**
- **JavaScript/TypeScript**: 2 spaces
- **HTML/CSS**: 2 spaces
- **YAML**: 2 spaces (required)
- **Lua**: 2 spaces

### **Ruff Configuration File (`ruff.toml`):**

```toml
# Optimized for Python fullstack web development
target-version = "py311"
line-length = 88
fix = true
show-fixes = true

[lint]
select = [
    "F",    # Pyflakes
    "E", "W", # pycodestyle
    "I",    # isort
    "N",    # pep8-naming
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "SIM",  # flake8-simplify
    "C4",   # flake8-comprehensions
    "PIE",  # flake8-pie
    "RET",  # flake8-return
    "DJ",   # flake8-django (Django projects)
    "PT",   # flake8-pytest-style
    "G",    # flake8-logging-format
    "Q",    # flake8-quotes
    "ANN",  # flake8-annotations (FastAPI projects)
    "ASYNC", # flake8-async (FastAPI projects)
    "S",    # flake8-bandit (security)
]

# Project-specific ignores
ignore = [
    "E501",  # Line too long (handled by formatter)
    "T201",  # Print statements (allowed in development)
]

[format]
quote-style = "double"
indent-style = "space"  # Ruff defaults to 4 spaces for Python (PEP 8)
skip-string-normalization = false
line-ending = "auto"
```

## ⚡ **BIOME CONFIGURATION - Complete Prettier Replacement**

### **Biome Handles:**
- **JavaScript** (.js, .jsx)
- **TypeScript** (.ts, .tsx)
- **JSON** (.json)
- **React/Vue Components**

### **Biome Commands:**

```vim
:BiomeFormat    " Format JS/TS/JSON with Biome
:BiomeLint      " Lint with Biome
:BiomeCheck     " Format + lint with Biome
:JSWorkflow     " Complete JS/TS workflow (format + lint)
```

### **Biome Keybindings:**

```vim
<leader>bf      " BiomeFormat
<leader>bl      " BiomeLint
<leader>bc      " BiomeCheck
<leader>jw      " JSWorkflow
```

### **Biome Configuration (`biome.json`):**

```json
{
  "$schema": "https://biomejs.dev/schemas/2.0.0/schema.json",
  "formatter": {
    "enabled": true,
    "indentWidth": 2,
    "lineWidth": 100
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true
    }
  },
  "javascript": {
    "formatter": {
      "semicolons": "always",
      "trailingCommas": "es5",
      "quoteStyle": "double"
    }
  }
}
```

### **Performance Benefits:**
- **10-100x faster** than Prettier
- **Single tool** for formatting and linting
- **Rust-based** for maximum performance
- **Zero configuration** needed for basic usage

## 🔧 **UNIVERSAL SMART FORMATTING**

### **Smart Format Command:**

The `:Format` command automatically detects file type and uses the appropriate formatter:

```vim
:Format         " Smart format command
<leader>f       " Smart format keybinding
```

### **File Type Detection:**

```lua
-- Smart formatting logic
if filetype == "python" then
    -- Use Ruff for Python
    require("config.ruff-manual").format_python()
elseif filetype in ["javascript", "typescript", "javascriptreact", "typescriptreact", "json"] then
    -- Use Biome for JS/TS/JSON
    conform.format({ formatters = { "biome" }, async = true, lsp_fallback = true })
elseif filetype == "lua" then
    -- Use Stylua for Lua
    conform.format({ formatters = { "stylua" }, async = true, lsp_fallback = true })
else
    -- Use LSP formatting for everything else
    vim.lsp.buf.format({ async = true })
end
```

## 📁 **COMPLETE FILE TYPE HANDLING STRATEGY**

| File Type | Formatter | Speed | Features |
|-----------|-----------|-------|----------|
| **Python** (.py) | Ruff | ⚡⚡⚡ | Format + Import Organization + Linting + Project Detection |
| **JavaScript** (.js, .jsx) | Biome | ⚡⚡⚡ | Format + Linting + Import Organization |
| **TypeScript** (.ts, .tsx) | Biome | ⚡⚡⚡ | Format + Linting + Import Organization |
| **JSON** (.json) | Biome | ⚡⚡⚡ | Format + Validation |
| **Lua** (.lua) | Stylua | ⚡⚡ | Format |
| **HTML** (.html) | LSP | ⚡ | Native Editor Formatting |
| **CSS** (.css, .scss) | LSP | ⚡ | Native Editor Formatting |
| **YAML** (.yml, .yaml) | LSP | ⚡ | Native Editor Formatting |
| **Markdown** (.md) | LSP | ⚡ | Native Editor Formatting |

## 🔄 **DEVELOPMENT WORKFLOWS**

### **Python Development Workflow:**

```vim
<leader>pw      " Complete Python workflow
:PythonWorkflow " Command version

# Executes:
# 1. Format code with Ruff
# 2. Organize imports (project-aware)
# 3. Lint with project-specific rules
```

### **JavaScript/TypeScript Development Workflow:**

```vim
<leader>jw      " Complete JS/TS workflow
:JSWorkflow     " Command version

# Executes:
# 1. Format code with Biome
# 2. Lint with Biome
# 3. Organize imports automatically
```

### **Universal Development Workflow:**

```vim
<leader>dw      " Universal workflow
:DevWorkflow    " Command version

# Auto-detects file type and runs appropriate workflow:
# - Python files → PythonWorkflow
# - JS/TS files → JSWorkflow
# - Other files → Smart format
```

## 🔧 **COMPLETION ENGINE - blink.cmp**

### **Modern Completion System:**

This configuration uses **blink.cmp** (LazyVim default) instead of nvim-cmp for better performance:

**Features:**
- ✅ **Faster completion** - Rust-based engine
- ✅ **Better LSP integration** - Optimized for modern LSP servers
- ✅ **LazyVim compatibility** - Seamless integration with LazyVim
- ✅ **Automatic configuration** - No manual setup required

**LSP Integration:**
```lua
-- Enhanced capabilities with blink.cmp (LazyVim default)
local capabilities = vim.lsp.protocol.make_client_capabilities()

-- Try to get blink.cmp capabilities if available
local has_blink, blink = pcall(require, "blink.cmp")
if has_blink then
  capabilities = blink.get_lsp_capabilities(capabilities)
end
```

**Supported LSP Servers:**
- ✅ **pyright** (Python)
- ✅ **typescript-language-server** (TypeScript/JavaScript)
- ✅ **lua-language-server** (Lua)
- ✅ **taplo** (TOML)

## 🛠️ **INSTALLATION & SETUP**

### **Required Tools:**

```bash
# Install Ruff (Python)
pip install ruff

# Install Biome (JavaScript/TypeScript)
npm install -g @biomejs/biome

# Install Stylua (Lua)
cargo install stylua
# or
brew install stylua
```

### **Mason Packages:**

The following packages are automatically installed via Mason:

```lua
{
  -- Python ecosystem
  "ruff",                    -- Python formatter/linter
  "pyright",                 -- Python LSP

  -- Frontend ecosystem
  "typescript-language-server", -- TypeScript LSP
  "biome",                   -- JS/TS formatter/linter

  -- Lua ecosystem
  "stylua",                  -- Lua formatter
  "lua-language-server",     -- Lua LSP
}
```

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **Ruff not working:**
1. Check installation: `ruff --version`
2. Install if missing: `pip install ruff`
3. Check project detection: `:RuffConfig`
4. Verify working directory context: `:RuffConfig` shows project root
5. Ensure config file exists: `ruff.toml` or `pyproject.toml` in project root
6. Restart LSP: `<leader>lr`

#### **Biome not working:**
1. Check installation: `biome --version`
2. Install if missing: `npm install -g @biomejs/biome`
3. Check file type: Biome only supports JS/TS/JSON
4. Use manual command: `:BiomeFormat`

#### **Smart format not working:**
1. Check file type: `:lua print(vim.bo.filetype)`
2. Use specific formatter: `:RuffFormat` or `:BiomeFormat`
3. Check LSP status: `:LspInfo`
4. Reload config: `<leader>lR`

#### **Working directory issues:**
1. Use `:RuffConfig` to verify project root detection
2. Ensure config files (`ruff.toml`, `pyproject.toml`) are in project root
3. Check if opened file is in a subdirectory of the project
4. Commands automatically find project root - no manual `cd` needed
5. If project detection fails, create `ruff.toml` in project root

#### **LuaRocks issues (FIXED):**
1. **Error**: `luarocks not installed` or `lua version 5.1 not installed`
2. **Solution**: Automatic hererocks environment setup
3. **Location**: `~/.local/share/nvim/lazy-rocks/`
4. **Verification**: `~/.local/share/nvim/lazy-rocks/bin/luarocks --version`
5. **Note**: LazyVim now supports plugins requiring LuaRocks

### **Diagnostic Commands:**

```vim
:MasonDiagnose  " Check Mason installation issues
<leader>lr      " Restart LSP
<leader>lR      " Reload Neovim config
:LspInfo        " Check LSP status
:ConformInfo    " Check formatter status
```

## 📈 **PERFORMANCE OPTIMIZATIONS**

### **Speed Improvements:**
- **Ruff**: 10-100x faster than Black + isort + flake8
- **Biome**: 10-100x faster than ESLint + Prettier
- **Native LSP**: Built-in editor formatting
- **Async formatting**: Non-blocking operations

### **Memory Efficiency:**
- **Rust-based tools** (Ruff, Biome) use less memory than Python/Node.js tools
- **Single tools** instead of multiple separate tools
- **LSP fallback** uses native editor resources

## 🎯 **BEST PRACTICES**

### **Daily Usage:**
1. Use `<leader>f` for quick formatting (most common)
2. Use `<leader>pw` for complete Python workflow
3. Use `<leader>jw` for complete JS/TS workflow
4. Use `<leader>dw` for universal workflow

### **Project Setup:**
1. Create `ruff.toml` for Python project-specific rules
2. Create `biome.json` for JS/TS project-specific rules
3. Let project detection handle the rest automatically

### **Performance Tips:**
1. Use async formatting (enabled by default)
2. Format on save is optional (disabled by default)
3. Use specific formatters for large files
4. Restart LSP if formatting becomes slow

## 🎉 **SUMMARY**

This configuration provides:

✅ **Fast, modern formatting** with Ruff and Biome
✅ **Project-aware Python development** with Django/FastAPI detection
✅ **Complete Prettier replacement** with Biome
✅ **Smart file type detection** and appropriate formatter selection
✅ **Comprehensive workflows** for different development scenarios
✅ **LSP fallback** for unsupported file types
✅ **Zero configuration** needed for basic usage
✅ **Professional development experience** optimized for fullstack Python development

**Start using: Open any file and press `<leader>f` for smart formatting!**
