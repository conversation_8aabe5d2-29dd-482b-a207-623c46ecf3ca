{"extras": ["lazyvim.plugins.extras.ai.copilot", "lazyvim.plugins.extras.ai.copilot-chat", "lazyvim.plugins.extras.coding.mini-surround", "lazyvim.plugins.extras.coding.yanky", "lazyvim.plugins.extras.editor.harpoon2", "lazyvim.plugins.extras.editor.inc-rename", "lazyvim.plugins.extras.editor.mini-diff", "lazyvim.plugins.extras.editor.overseer", "lazyvim.plugins.extras.editor.telescope", "lazyvim.plugins.extras.formatting.biome", "lazyvim.plugins.extras.lang.go", "lazyvim.plugins.extras.lang.markdown", "lazyvim.plugins.extras.lang.sql", "lazyvim.plugins.extras.lang.svelte", "lazyvim.plugins.extras.lang.tailwind", "lazyvim.plugins.extras.lang.toml", "lazyvim.plugins.extras.lsp.neoconf", "lazyvim.plugins.extras.ui.mini-animate", "lazyvim.plugins.extras.ui.treesitter-context", "lazyvim.plugins.extras.util.dot", "lazyvim.plugins.extras.util.gitui", "lazyvim.plugins.extras.util.mini-hipatterns", "lazyvim.plugins.extras.util.octo", "lazyvim.plugins.extras.util.rest"], "install_version": 7, "news": {"NEWS.md": "10960"}, "version": 8}