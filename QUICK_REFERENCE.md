# 🚀 Neovim Quick Reference - Python Fullstack Development

## ⚡ **SMART FORMATTING (Most Used)**

```vim
<leader>f       " Smart format (auto-detects: Python→Ruff, JS/TS→Biome, Lua→Stylua, Others→LSP)
:Format         " Same as above (command version)
```

## 🐍 **PYTHON (RUFF) - Project-Aware**

### **Commands:**
```vim
:RuffFormat     " Format Python (detects Django/FastAPI/General)
:RuffImports    " Organize imports (project-aware)
:RuffAll        " Format + organize imports
:RuffLint       " Lint with project-specific rules
:RuffConfig     " Show current project configuration
:PythonWorkflow " Complete workflow (format + organize + lint)
```

### **Keybindings:**
```vim
<leader>rf      " RuffFormat
<leader>ri      " RuffImports
<leader>ra      " RuffAll (format + organize)
<leader>rl      " RuffLint
<leader>rc      " RuffConfig
<leader>pw      " PythonWorkflow
```

## ⚡ **JAVASCRIPT/TYPESCRIPT (BIOME) - No Prettier**

### **Commands:**
```vim
:BiomeFormat    " Format JS/TS/JSON with Biome
:BiomeLint      " Lint with Biome
:BiomeCheck     " Format + lint with Biome
:JSWorkflow     " Complete JS/TS workflow (format + lint)
```

### **Keybindings:**
```vim
<leader>bf      " BiomeFormat
<leader>bl      " BiomeLint
<leader>bc      " BiomeCheck
<leader>jw      " JSWorkflow
```

## 🔧 **UNIVERSAL WORKFLOWS**

```vim
<leader>dw      " DevWorkflow (auto-detects language)
:DevWorkflow    " Smart workflow for any file type
```

## 📁 **FILE TYPE HANDLING**

| File Type | Formatter | Command | Keybing |
|-----------|-----------|---------|---------|
| **Python** (.py) | Ruff | `:RuffAll` | `<leader>ra` |
| **JavaScript** (.js, .jsx) | Biome | `:BiomeFormat` | `<leader>bf` |
| **TypeScript** (.ts, .tsx) | Biome | `:BiomeFormat` | `<leader>bf` |
| **JSON** (.json) | Biome | `:BiomeFormat` | `<leader>bf` |
| **Lua** (.lua) | Stylua | `:Format` | `<leader>f` |
| **HTML/CSS/YAML** | LSP | `:Format` | `<leader>f` |

## 🎯 **PROJECT DETECTION (RUFF)**

- **Django**: Detects `manage.py` or `settings.py` → Uses Django-specific rules
- **FastAPI**: Detects `main.py` with FastAPI imports → Uses async/type annotation rules
- **General**: Default Python project → Uses standard rules

## 🔥 **DAILY WORKFLOW**

### **Python Development:**
```vim
# Open Python file
nvim models.py

# Complete Python workflow
<leader>pw      # Format + organize imports + lint

# Or step by step
<leader>rf      # Format
<leader>ri      # Organize imports
<leader>rl      # Lint
```

### **JavaScript/TypeScript Development:**
```vim
# Open JS/TS file
nvim component.tsx

# Complete JS/TS workflow
<leader>jw      # Format + lint with Biome

# Or step by step
<leader>bf      # Format
<leader>bl      # Lint
```

### **Universal (Any File):**
```vim
# Open any file
nvim any-file.ext

# Smart format (auto-detects)
<leader>f       # Uses appropriate formatter

# Smart workflow
<leader>dw      # Complete workflow for file type
```

## 🛠️ **DEVELOPMENT COMMANDS**

### **Django:**
```vim
<leader>Pm      " Django makemigrations
<leader>PM      " Django migrate
<leader>Pr      " Django runserver
```

### **FastAPI:**
```vim
<leader>Fr      " FastAPI run (uvicorn)
```

### **Frontend:**
```vim
<leader>Nd      " npm run dev
<leader>Nb      " npm run build
<leader>Nt      " npm test
```

### **Docker:**
```vim
<leader>Du      " Docker compose up
<leader>Dd      " Docker compose down
```

## 🔍 **DIAGNOSTICS & TROUBLESHOOTING**

```vim
<leader>lr      " Restart LSP
<leader>lR      " Reload Neovim config
:MasonDiagnose  " Check Mason installation issues
```

## 📋 **CONFIGURATION STATUS**

### **✅ Installed & Configured:**
- **Ruff**: Python formatting, import organization, linting (project-aware)
- **Biome**: JS/TS/JSON formatting and linting (replaces ESLint + Prettier)
- **Stylua**: Lua formatting
- **LSP**: Fallback formatting for HTML/CSS/YAML/Markdown

### **❌ Removed:**
- **Prettier**: Completely removed (Biome replacement)
- **ESLint**: Replaced by Biome

## 🎯 **REMEMBER:**

1. **`<leader>f`** - Universal smart format (most used)
2. **`<leader>pw`** - Python complete workflow
3. **`<leader>jw`** - JavaScript/TypeScript complete workflow
4. **`<leader>dw`** - Universal development workflow

**Your leader key is `<Space>` - so `<leader>f` = `Space + f`**
