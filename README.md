# 💤 LazyThaytoVim

A starter template for [LazyVim](https://github.com/LazyVim/LazyVim).
Refer to the [documentation](https://lazyvim.github.io/installation) to get started.

## 🎥 Check out my videos

- [VIM/Neovim first steps - Zero to hero](https://youtu.be/7U9fVUFYx90?si=a0vMktzzFF3mSQo1)
- [How to install plugins in Neovim using lazy.nvim](https://youtu.be/W7Jf4bnPcXY?si=ZnwscS28OcrO2AcM)
- [VIM/Neovim advanced horizontal moves](https://youtu.be/UGtAOHMKvSw?si=seEzJ6hX_hmjbhK-)
- [How to install Vim/Neovim on Windows](https://youtu.be/gwVe00HVvrk?si=5Bk-a58woBeKTsbF)
- and much more on my [YouTube Channel](https://www.youtube.com/@thayto_dev)...

## ⚡️ Requirements

- Neovim >= **0.9.0** (needs to be built with **LuaJIT**)
- Git >= **2.19.0** (for partial clones support)
- a [Nerd Font](https://www.nerdfonts.com/) **_(optional)_**
- a **C** compiler for `nvim-treesitter`. See [here](https://github.com/nvim-treesitter/nvim-treesitter#requirements)

## 🚀 Be happy 🥰

- Make a backup of your current Neovim files:

  **Linux/Mac OS:**
  ```sh
  mv ~/.config/nvim ~/.config/nvim.bak
  mv ~/.local/share/nvim ~/.local/share/nvim.bak
  ```

  **Windows:**
  ```sh
  mv $env:LOCALAPPDATA\nvim $env:LOCALAPPDATA\nvim.bak
  mv $env:LOCALAPPDATA\nvim-data $env:LOCALAPPDATA\nvim-data.bak
  ```

- Clone the repo

  **Linux/Mac OS**
  ```sh
  git clone https://github.com/rafa-thayto/lazy-thayto-vim ~/.config/nvim
  ```

  **Windows:**
  ```sh
  git clone https://github.com/rafa-thayto/lazy-thayto-vim $env:LOCALAPPDATA\nvim
  ```
