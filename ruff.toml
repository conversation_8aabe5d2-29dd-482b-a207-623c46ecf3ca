# Ruff Configuration for Python Fullstack Web Development
# Optimized for Django/FastAPI + Frontend development

# Target Python 3.11+ for modern web development
target-version = "py311"

# Line length - Django/Black standard
line-length = 88

# Enable auto-fixing for specific rule categories
fix = true
show-fixes = true

# Formatação e organização de imports
[format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[lint]
select = [
    "F",    # Pyflakes
    "E", "W", # pycodestyle
    "I",    # isort
    "N",    # pep8-naming
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "SIM",  # flake8-simplify
    "C4",   # flake8-comprehensions
    "PIE",  # flake8-pie
    "RET",  # flake8-return
    "DJ",   # flake8-django (Django projects)
    "PT",   # flake8-pytest-style
    "ANN",  # flake8-annotations (FastAPI projects)
]
